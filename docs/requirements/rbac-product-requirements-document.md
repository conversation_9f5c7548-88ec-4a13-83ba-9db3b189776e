# Role-Based Access Control (RBAC) Product Requirements Document
## CarbonX Carbon Credit Trading Platform

**Document Version:** 1.0  
**Date:** July 18, 2025  
**Last Updated:** July 18, 2025  
**Product Manager:** [To be filled]  
**Security Lead:** [To be filled]  
**Engineering Lead:** [To be filled]  

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [RBAC Overview](#rbac-overview)
3. [User Roles and Permissions](#user-roles-and-permissions)
4. [Role-Specific Dashboards](#role-specific-dashboards)
5. [Permission Matrix](#permission-matrix)
6. [Technical Implementation](#technical-implementation)
7. [Security Requirements](#security-requirements)
8. [Acceptance Criteria](#acceptance-criteria)

---

## 1. Executive Summary

### 1.1 Purpose
This document defines the comprehensive Role-Based Access Control (RBAC) system for the CarbonX platform, ensuring secure, granular access control across all user types including enterprise users, brokers, and platform administrators.

### 1.2 Scope
The RBAC system covers:
- **14 distinct user roles** with specific permissions and capabilities
- **Role-specific dashboards** tailored to each user type
- **Granular permission system** with 50+ individual permissions
- **Multi-tenant organization management** with data isolation
- **Broker-specific access controls** for client management
- **Audit trails** for all permission changes and access attempts

### 1.3 Key Benefits
- **Enhanced Security**: Principle of least privilege access
- **Operational Efficiency**: Role-appropriate interfaces and workflows
- **Compliance**: Audit trails and permission tracking
- **Scalability**: Support for 1000+ organizations with proper isolation
- **Flexibility**: Custom roles and temporary permission grants

---

## 2. RBAC Overview

### 2.1 RBAC Architecture
The CarbonX RBAC system implements a hierarchical role-based access control model with:

- **Predefined Roles**: 14 standard roles covering all platform functions
- **Custom Roles**: Organization-specific roles with granular permissions
- **Permission Inheritance**: Hierarchical permission structure
- **Temporary Grants**: Time-limited permission assignments
- **Multi-tenancy**: Organization-level data isolation

### 2.2 Core Principles
1. **Principle of Least Privilege**: Users receive minimum permissions required
2. **Separation of Duties**: Critical operations require multiple roles
3. **Data Isolation**: Complete separation between organizations
4. **Audit Transparency**: All access and changes are logged
5. **Role Hierarchy**: Structured permission inheritance

---

## 3. User Roles and Permissions

### 3.1 Platform Administrator Roles

#### 3.1.1 Platform Administrator
- **Description**: Carbonix operations team member with full platform access
- **Key Responsibilities**:
  - Platform health monitoring and maintenance
  - Customer support and issue resolution
  - System configuration and updates
  - Cross-organization analytics and reporting
- **Access Level**: Global platform access
- **Dashboard**: Platform-wide monitoring and administration

#### 3.1.2 Compliance Officer (Platform Level)
- **Description**: Platform-wide compliance and regulatory oversight
- **Key Responsibilities**:
  - Regulatory compliance monitoring
  - KYC/AML verification oversight
  - Audit trail management
  - Risk assessment and reporting
- **Access Level**: Read-only access to all organizations for compliance
- **Dashboard**: Compliance monitoring and regulatory reporting

### 3.2 Enterprise Organization Roles

#### 3.2.1 Organization Admin
- **Description**: Senior executive overseeing organization's carbon strategy
- **Key Responsibilities**:
  - Organization setup and configuration
  - Team member management and role assignment
  - Strategic decision making and reporting
  - Budget oversight and financial planning
- **Access Level**: Full access within organization
- **Dashboard**: Executive overview with organization-wide metrics

#### 3.2.2 Carbon Credit Manager
- **Description**: Professional managing carbon credit portfolio and projects
- **Key Responsibilities**:
  - Project creation and lifecycle management
  - Carbon credit generation and verification
  - Marketplace trading and optimization
  - Performance tracking and reporting
- **Access Level**: Full project and credit management within organization
- **Dashboard**: Project portfolio and credit pipeline management

#### 3.2.3 Wallet Manager
- **Description**: Blockchain operations specialist managing digital assets
- **Key Responsibilities**:
  - Wallet creation and security management
  - Cross-chain transaction execution
  - Gas fee optimization
  - Security monitoring and incident response
- **Access Level**: Full wallet and blockchain operations within organization
- **Dashboard**: Multi-chain wallet overview and transaction management

#### 3.2.4 Finance Manager
- **Description**: Financial controller overseeing carbon asset finances
- **Key Responsibilities**:
  - Financial performance tracking
  - Cost analysis and budget management
  - Asset valuation monitoring (including INR valuations)
  - ROI analysis and financial reporting
- **Access Level**: Financial data and transaction monitoring within organization
- **Dashboard**: Financial metrics and asset valuation tracking

#### 3.2.5 Compliance Officer (Organization Level)
- **Description**: Organization's regulatory compliance manager
- **Key Responsibilities**:
  - Internal compliance monitoring
  - Risk management and mitigation
  - Audit preparation and documentation
  - Regulatory reporting coordination
- **Access Level**: Compliance data and audit trails within organization
- **Dashboard**: Compliance status and risk monitoring

#### 3.2.6 Team Manager
- **Description**: Mid-level manager overseeing specific teams or departments
- **Key Responsibilities**:
  - Team coordination and task assignment
  - Progress monitoring and reporting
  - Resource allocation within team scope
  - Performance evaluation and feedback
- **Access Level**: Team-specific project and user management
- **Dashboard**: Team performance and project status

#### 3.2.7 Department Admin
- **Description**: Department-level administrator with broader scope than Team Manager
- **Key Responsibilities**:
  - Department-wide project oversight
  - Cross-team coordination
  - Department budget and resource management
  - Strategic planning within department scope
- **Access Level**: Department-wide project and team management
- **Dashboard**: Department overview with multi-team metrics

#### 3.2.8 Division Admin
- **Description**: Senior administrator managing multiple departments
- **Key Responsibilities**:
  - Division-wide strategic planning
  - Cross-department coordination
  - High-level resource allocation
  - Executive reporting and communication
- **Access Level**: Division-wide access across multiple departments
- **Dashboard**: Division-level strategic overview

#### 3.2.9 Regular User
- **Description**: Standard platform user executing day-to-day tasks
- **Key Responsibilities**:
  - Task execution and data entry
  - Basic reporting and documentation
  - Project participation and updates
  - Learning and skill development
- **Access Level**: Limited access to assigned projects and tasks
- **Dashboard**: Personal task management and project participation

#### 3.2.10 Read-Only User
- **Description**: User with view-only access for monitoring and reporting
- **Key Responsibilities**:
  - Data monitoring and observation
  - Report generation and analysis
  - Stakeholder communication
  - Compliance verification
- **Access Level**: Read-only access to assigned data and reports
- **Dashboard**: Monitoring and reporting interface

### 3.3 Broker System Roles

#### 3.3.1 Independent Broker
- **Description**: Autonomous broker managing client relationships independently
- **Key Responsibilities**:
  - Client acquisition and onboarding
  - Project management for multiple clients
  - Direct marketplace trading
  - Commission optimization and tracking
- **Access Level**: Full client management and trading capabilities
- **Dashboard**: Client portfolio and commission tracking

#### 3.3.2 Onix-Managed Broker
- **Description**: Broker operating under Onix partnership model
- **Key Responsibilities**:
  - Client data submission to Onix
  - Project status monitoring and reporting
  - Client communication and relationship maintenance
  - Performance tracking and optimization
- **Access Level**: Limited to data submission and status tracking
- **Dashboard**: Client status and earnings tracking

#### 3.3.3 Broker Client
- **Description**: Organization receiving services through broker relationship
- **Key Responsibilities**:
  - Project data provision and collaboration
  - Broker communication and feedback
  - Compliance with broker requirements
  - Strategic decision making with broker guidance
- **Access Level**: Project participation with broker oversight
- **Dashboard**: Project progress with broker assistance indicators

### 3.4 Specialized Roles

#### 3.4.1 SPV User
- **Description**: Special Purpose Vehicle user with specific project access
- **Key Responsibilities**:
  - SPV-specific project management
  - Limited scope financial operations
  - Compliance with SPV requirements
  - Stakeholder reporting and communication
- **Access Level**: SPV-specific project and financial data
- **Dashboard**: SPV project and financial overview

#### 3.4.2 SPV Admin
- **Description**: Administrator managing SPV operations and compliance
- **Key Responsibilities**:
  - SPV setup and configuration
  - Compliance monitoring and reporting
  - Financial oversight and control
  - Stakeholder management and communication
- **Access Level**: Full SPV management and administration
- **Dashboard**: SPV administration and compliance monitoring

#### 3.4.3 Site Worker
- **Description**: On-site personnel managing physical project operations
- **Key Responsibilities**:
  - On-site data collection and monitoring
  - Equipment maintenance and operation
  - Safety compliance and reporting
  - Real-time status updates and communication
- **Access Level**: Site-specific data entry and monitoring
- **Dashboard**: Site operations and data collection interface

#### 3.4.4 Project Manager
- **Description**: Professional managing specific carbon credit projects
- **Key Responsibilities**:
  - Project planning and execution
  - Timeline and milestone management
  - Resource coordination and allocation
  - Stakeholder communication and reporting
- **Access Level**: Full project management within assigned projects
- **Dashboard**: Project timeline and resource management

---

## 4. Role-Specific Dashboards

### 4.1 Dashboard Design Principles
- **Role-Appropriate Content**: Each dashboard shows only relevant information
- **Actionable Insights**: Dashboards enable quick decision-making
- **Real-Time Updates**: Live data feeds for critical metrics
- **Customizable Widgets**: Users can personalize their dashboard layout
- **Mobile Responsive**: Consistent experience across all devices

### 4.2 Dashboard Components by Role

#### 4.2.1 Organization Admin Dashboard
- Organization health metrics and KPIs
- Team activity and performance summaries
- Financial overview and budget tracking
- Compliance status indicators
- Recent notifications and alerts
- Strategic analytics and trends

#### 4.2.2 Carbon Credit Manager Dashboard
- Project portfolio overview and status
- Credit generation pipeline and forecasts
- Marketplace performance metrics
- Trading opportunities and alerts
- Verification status and timelines
- Performance analytics and optimization

#### 4.2.3 Wallet Manager Dashboard
- Multi-chain wallet overview and balances
- Transaction history and pending operations
- Security status and threat alerts
- Gas fee optimization recommendations
- Cross-chain bridge status and opportunities
- Network performance and connectivity

#### 4.2.4 Finance Manager Dashboard
- Financial performance metrics and trends
- Cost analysis and budget tracking
- Asset valuation with real-time INR mapping
- Transaction fees and cost optimization
- ROI analysis and projections
- Financial compliance and reporting

#### 4.2.5 Independent Broker Dashboard
- Client portfolio overview and performance
- Commission earnings and payment status
- Active projects and credit generation pipeline
- Marketplace opportunities and client matches
- Client onboarding progress and assistance tools
- Performance metrics and optimization insights

#### 4.2.6 Onix-Managed Broker Dashboard
- Client data submission interface
- Project status tracking and updates
- Commission earnings from Onix partnerships
- Client communication and reporting tools
- Performance metrics and client satisfaction
- Onix coordination and support tools

### 4.3 Dashboard Customization
- **Widget Selection**: Users can add/remove dashboard widgets
- **Layout Preferences**: Drag-and-drop widget arrangement
- **Data Filters**: Customizable time ranges and data filters
- **Alert Configuration**: Personalized notification settings
- **Export Options**: Dashboard data export capabilities

---

## 5. Permission Matrix

### 5.1 Core Permission Categories

#### 5.1.1 User Management Permissions
- `user:create` - Create new users
- `user:read` - View user information
- `user:update` - Modify user details
- `user:delete` - Remove users
- `user:assign_roles` - Assign roles to users
- `user:manage_permissions` - Modify user permissions

#### 5.1.2 Organization Management Permissions
- `org:create` - Create organizations
- `org:read` - View organization details
- `org:update` - Modify organization settings
- `org:delete` - Remove organizations
- `org:manage_settings` - Configure organization preferences
- `org:view_analytics` - Access organization analytics

#### 5.1.3 Project Management Permissions
- `project:create` - Create new projects
- `project:read` - View project information
- `project:update` - Modify project details
- `project:delete` - Remove projects
- `project:manage_documents` - Upload/manage project documents
- `project:submit_verification` - Submit for verification
- `project:approve_verification` - Approve project verification

#### 5.1.4 Carbon Credit Permissions
- `credit:create` - Generate carbon credits
- `credit:read` - View credit information
- `credit:update` - Modify credit details
- `credit:tokenize` - Convert credits to tokens
- `credit:retire` - Retire carbon credits
- `credit:trade` - Buy/sell credits in marketplace

#### 5.1.5 Wallet and Blockchain Permissions
- `wallet:create` - Create new wallets
- `wallet:read` - View wallet information
- `wallet:send` - Send transactions
- `wallet:receive` - Receive transactions
- `wallet:manage_keys` - Manage private keys
- `wallet:cross_chain` - Execute cross-chain transfers

#### 5.1.6 Marketplace Permissions
- `marketplace:list` - List credits for sale
- `marketplace:buy` - Purchase credits
- `marketplace:sell` - Sell credits
- `marketplace:view_orders` - View order book
- `marketplace:manage_orders` - Create/modify orders
- `marketplace:view_analytics` - Access market analytics

#### 5.1.7 Compliance Permissions
- `compliance:view_kyc` - View KYC information
- `compliance:approve_kyc` - Approve KYC submissions
- `compliance:view_audit_logs` - Access audit trails
- `compliance:generate_reports` - Create compliance reports
- `compliance:manage_risk` - Risk assessment and management

#### 5.1.8 Broker-Specific Permissions
- `broker:manage_clients` - Manage client relationships
- `broker:invite_clients` - Invite new clients
- `broker:view_commissions` - View commission earnings
- `broker:submit_data` - Submit client data (Onix-managed)
- `broker:access_marketplace` - Access trading features
- `broker:manage_projects` - Manage client projects

### 5.2 Permission Inheritance Hierarchy

```
Organization Admin
├── Carbon Credit Manager
│   ├── Project Manager
│   └── Regular User
├── Finance Manager
│   └── Read-Only User
├── Wallet Manager
├── Compliance Officer
├── Team Manager
│   ├── Department Admin
│   │   └── Division Admin
│   └── Regular User
└── Independent Broker
    └── Broker Client

Platform Administrator
├── Compliance Officer (Platform)
└── All Organization Roles (Read-Only)
```

### 5.3 Role-Permission Matrix

| Permission Category | Org Admin | Credit Mgr | Wallet Mgr | Finance Mgr | Compliance | Broker | Regular User |
|-------------------|-----------|------------|------------|-------------|------------|--------|--------------|
| User Management | Full | Limited | None | None | Read | Limited | None |
| Project Management | Full | Full | None | Read | Read | Full* | Limited |
| Carbon Credits | Full | Full | None | Read | Read | Full* | Limited |
| Wallet Operations | Read | Limited | Full | Read | Read | Limited | None |
| Marketplace | Full | Full | Limited | Read | Read | Full* | Limited |
| Compliance | Read | Read | Read | Read | Full | Read | None |
| Analytics | Full | Full | Limited | Full | Full | Limited | Limited |

*Broker permissions apply to client organizations only

---

## 6. Technical Implementation

### 6.1 Database Schema

#### 6.1.1 User Roles Table
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    organization_id UUID REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 6.1.2 Permissions Table
```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 6.1.3 Role Permissions Table
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID REFERENCES user_roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    UNIQUE(role_id, permission_id)
);
```

#### 6.1.4 User Role Assignments Table
```sql
CREATE TABLE user_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES user_roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, role_id)
);
```

### 6.2 API Implementation

#### 6.2.1 Permission Check Middleware
```typescript
async function checkPermission(
  userId: string,
  organizationId: string,
  permission: string,
  resourceId?: string
): Promise<boolean> {
  // Get user roles within organization
  const userRoles = await getUserRoles(userId, organizationId);
  
  // Check if any role has the required permission
  for (const role of userRoles) {
    const hasPermission = await roleHasPermission(role.id, permission);
    if (hasPermission) {
      // Additional resource-level checks if needed
      if (resourceId) {
        return await checkResourceAccess(userId, resourceId, permission);
      }
      return true;
    }
  }
  
  return false;
}
```

#### 6.2.2 Role Assignment API
```typescript
POST /api/v1/users/{userId}/roles
{
  "roleId": "role_uuid",
  "expiresAt": "2024-12-31T23:59:59Z" // Optional
}
```

### 6.3 Caching Strategy
- **User Permissions**: Cached for 15 minutes with Redis
- **Role Definitions**: Cached for 1 hour with automatic invalidation
- **Organization Hierarchy**: Cached for 30 minutes
- **Permission Matrix**: Cached for 2 hours with manual refresh

---

## 7. Security Requirements

### 7.1 Access Control Security
- **Multi-Factor Authentication**: Required for all administrative roles
- **Session Management**: Secure session handling with automatic timeout
- **Permission Validation**: Server-side permission checks for all operations
- **Audit Logging**: Complete audit trail for all permission changes

### 7.2 Data Protection
- **Row-Level Security**: PostgreSQL RLS for multi-tenant data isolation
- **Encryption**: Sensitive permission data encrypted at rest
- **API Security**: Rate limiting and input validation for all endpoints
- **Cross-Tenant Protection**: Strict isolation between organizations

### 7.3 Compliance Requirements
- **SOC 2 Type II**: Access control compliance
- **GDPR**: Data protection and user rights
- **ISO 27001**: Information security management
- **Regular Audits**: Quarterly security assessments

---

## 8. Acceptance Criteria

### 8.1 Functional Requirements
- [ ] All 14 user roles are implemented with correct permissions
- [ ] Role-specific dashboards display appropriate content
- [ ] Permission inheritance works correctly across role hierarchy
- [ ] Custom roles can be created with granular permissions
- [ ] Temporary permission grants expire automatically
- [ ] Multi-tenant data isolation is enforced
- [ ] Broker-specific permissions enable client management
- [ ] Audit trails capture all permission changes

### 8.2 Performance Requirements
- [ ] Permission checks complete within 50ms
- [ ] Dashboard loads within 2 seconds
- [ ] Role assignments process within 1 second
- [ ] Cache invalidation occurs within 30 seconds
- [ ] System supports 10,000+ concurrent users

### 8.3 Security Requirements
- [ ] All administrative actions require MFA
- [ ] Cross-tenant data access is prevented
- [ ] Permission escalation is not possible
- [ ] Audit logs are immutable and complete
- [ ] Security assessments pass with no critical issues

### 8.4 Usability Requirements
- [ ] Role assignment interface is intuitive
- [ ] Dashboard customization is user-friendly
- [ ] Permission errors provide clear guidance
- [ ] Mobile experience is fully functional
- [ ] Accessibility requirements are met (WCAG 2.1 AA)

---

**Document Control**
- **Version**: 1.0
- **Last Updated**: July 18, 2025
- **Next Review**: October 18, 2025
- **Owner**: Product Management Team
- **Contributors**: Security Team, Engineering Team, UX Team
- **Stakeholders**: Executive Team, Compliance Team, Customer Success Team
