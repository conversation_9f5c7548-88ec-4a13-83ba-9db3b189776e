[2025-07-18 12:01:23] === Prisma Migration Script ===
[2025-07-18 12:01:23] Checking if the database is running...
[2025-07-18 12:01:24] PostgreSQL container is not running. Starting it...
[2025-07-18 12:01:24] EXECUTING: docker-compose up -d db
$ docker-compose up -d db
The Compose file './docker-compose.yml' is invalid because:
services.app.environment contains non-unique items, please remove duplicates from ['DATABASE_URL=**************************************/carbon_exchange', 'DATABASE_HOST=db', 'DATABASE_PORT=5432', 'NODE_ENV=production', 'NEXTAUTH_URL=http://localhost:3000', 'NEXT_PUBLIC_API_URL=http://localhost:3000', 'EMAIL_SERVER=smtp.gmail.com', 'EMAIL_FROM=<EMAIL>', 'SMTP_HOST=smtp.gmail.com', 'SMTP_PORT=587', 'SMTP_USER=<EMAIL>', 'SMTP_PASSWORD=wzedkbjgitlmxnqj', 'SMTP_FROM=<EMAIL>', 'ALCHEMY_API_KEY=your-alchemy-api-key', 'ALCHEMY_NETWORK=eth-sepolia', 'ETHEREUM_NETWORK=sepolia', 'POLYGON_NETWORK=mumbai', 'OPTIMISM_NETWORK=optimism-sepolia', 'ARBITRUM_NETWORK=arbitrum-sepolia', 'BASE_NETWORK=base-sepolia', 'NODE_ENV=production', 'WALLET_ENCRYPTION_KEY=your-wallet-encryption-key', 'ALCHEMY_GAS_MANAGER_POLICY_ID=your-gas-manager-policy-id', 'ANALYTICS_ID=prod-analytics-id', 'STORAGE_PROVIDER=local', 'S3_BUCKET=', 'S3_REGION=', 'S3_ACCESS_KEY=', 'S3_SECRET_KEY=', 'PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x', 'PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1']
[2025-07-18 12:01:25] ERROR: Starting database container failed (exit code: 1)
[2025-07-18 12:01:25] Check the log file for details: logs/prisma_migrate_20250718_120123.log
[2025-07-18 12:01:25] Waiting for PostgreSQL to be ready...
