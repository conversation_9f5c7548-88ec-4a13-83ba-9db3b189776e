const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUsers() {
  try {
    console.log('Creating test users...');

    // Hash passwords
    const adminPassword = await bcrypt.hash('admin123456', 10);
    const orgAdminPassword = await bcrypt.hash('password123', 10);

    // Create Platform Admin
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Platform Administrator',
        password: adminPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
        jobTitle: 'Platform Administrator',
      },
    });

    console.log('✅ Created Platform Admin:', admin.email);

    // Create a test organization
    const organization = await prisma.organization.upsert({
      where: { name: 'Test Organization' },
      update: {},
      create: {
        name: 'Test Organization',
        description: 'A test organization for RBAC testing',
        type: 'ENTERPRISE',
        size: 'MEDIUM',
        industry: 'TECHNOLOGY',
        country: 'United States',
        website: 'https://testorg.com',
        isVerified: true,
        verificationStatus: 'VERIFIED',
      },
    });

    console.log('✅ Created Organization:', organization.name);

    // Create Organization Admin
    const orgAdmin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Organization Administrator',
        password: orgAdminPassword,
        role: 'ORGANIZATION_ADMIN',
        organizationId: organization.id,
        emailVerified: new Date(),
        jobTitle: 'Organization Administrator',
      },
    });

    console.log('✅ Created Organization Admin:', orgAdmin.email);

    // Initialize RBAC system
    console.log('Initializing RBAC system...');
    
    // Create some basic permissions
    const permissions = [
      { name: 'create:user', displayName: 'Create User', description: 'Create new users', category: 'user_management' },
      { name: 'read:user', displayName: 'View User', description: 'View user details', category: 'user_management' },
      { name: 'update:user', displayName: 'Update User', description: 'Update user details', category: 'user_management' },
      { name: 'delete:user', displayName: 'Delete User', description: 'Delete users', category: 'user_management' },
    ];

    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {},
        create: permission,
      });
    }

    // Create some basic roles
    const roles = [
      {
        name: 'ORGANIZATION_USER',
        displayName: 'Organization User',
        description: 'Standard organization user',
        isSystemRole: true,
        organizationId: null,
      },
      {
        name: 'TEAM_MANAGER',
        displayName: 'Team Manager',
        description: 'Manages a team within the organization',
        isSystemRole: true,
        organizationId: null,
      },
    ];

    for (const role of roles) {
      await prisma.customRole.upsert({
        where: { name: role.name },
        update: {},
        create: role,
      });
    }

    console.log('✅ RBAC system initialized');

    console.log('\n🎉 Test users created successfully!');
    console.log('\n📋 LOGIN CREDENTIALS:');
    console.log('======================');
    console.log('🔧 PLATFORM ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('');
    console.log('🏢 ORGANIZATION ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('');
    console.log('🌐 Access the application at: http://localhost:3000');
    console.log('📊 RBAC Module at: http://localhost:3000/dashboard/rbac');

  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();
