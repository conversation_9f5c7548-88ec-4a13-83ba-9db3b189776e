const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testAdminUserCreation() {
  try {
    console.log('🧪 Testing admin user creation and onboarding skip...');

    // Hash passwords
    const adminPassword = await bcrypt.hash('admin123456', 10);
    const testUserPassword = await bcrypt.hash('testuser123', 10);

    // Create Platform Admin if not exists
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Platform Administrator',
        password: adminPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
        jobTitle: 'Platform Administrator',
      },
    });

    console.log('✅ Platform Admin ready:', admin.email);

    // Create a test organization
    const organization = await prisma.organization.upsert({
      where: { name: 'Test Organization' },
      update: {},
      create: {
        name: 'Test Organization',
        description: 'A test organization for RBAC testing',
        type: 'ENTERPRISE',
        size: 'MEDIUM',
        industry: 'TECHNOLOGY',
        country: 'United States',
        website: 'https://testorg.com',
        isVerified: true,
        verificationStatus: 'VERIFIED',
      },
    });

    // Create Organization Admin
    const orgAdmin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Organization Administrator',
        password: adminPassword,
        role: 'ORGANIZATION_ADMIN',
        organizationId: organization.id,
        emailVerified: new Date(),
        jobTitle: 'Organization Administrator',
      },
    });

    console.log('✅ Organization Admin ready:', orgAdmin.email);

    // Create a test user that would be created by organization admin
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        password: testUserPassword,
        role: 'ORGANIZATION_USER',
        organizationId: organization.id,
        emailVerified: new Date(), // Admin-created users have verified email
        jobTitle: 'Test Employee',
      },
    });

    // Create onboarding state for admin-created user (skip onboarding)
    await prisma.onboardingState.upsert({
      where: { userId: testUser.id },
      update: {
        currentStep: 'complete',
        completedSteps: JSON.stringify(['organization_details', 'team_invitations', 'subscription', 'wallet_setup', 'verification']),
        skippedSteps: JSON.stringify([]),
      },
      create: {
        userId: testUser.id,
        currentStep: 'complete',
        organizationId: organization.id,
        completedSteps: JSON.stringify(['organization_details', 'team_invitations', 'subscription', 'wallet_setup', 'verification']),
        skippedSteps: JSON.stringify([]),
      },
    });

    console.log('✅ Test user created with onboarding skipped:', testUser.email);

    // Create some basic permissions and roles for testing
    const permissions = [
      { name: 'create:user', displayName: 'Create User', description: 'Create new users', category: 'user_management' },
      { name: 'read:user', displayName: 'View User', description: 'View user details', category: 'user_management' },
      { name: 'update:user', displayName: 'Update User', description: 'Update user details', category: 'user_management' },
      { name: 'delete:user', displayName: 'Delete User', description: 'Delete users', category: 'user_management' },
    ];

    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {},
        create: permission,
      });
    }

    // Create some basic roles
    const roles = [
      {
        name: 'ORGANIZATION_USER',
        displayName: 'Organization User',
        description: 'Standard organization user',
        isSystemRole: true,
        organizationId: null,
      },
      {
        name: 'TEAM_MANAGER',
        displayName: 'Team Manager',
        description: 'Manages a team within the organization',
        isSystemRole: true,
        organizationId: null,
      },
    ];

    for (const role of roles) {
      await prisma.customRole.upsert({
        where: { name: role.name },
        update: {},
        create: role,
      });
    }

    console.log('✅ RBAC system initialized');

    console.log('\n🎉 Test setup complete!');
    console.log('\n📋 TEST LOGIN CREDENTIALS:');
    console.log('=========================');
    console.log('🔧 PLATFORM ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('   Expected: Direct to dashboard');
    console.log('');
    console.log('🏢 ORGANIZATION ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('   Expected: Direct to dashboard');
    console.log('');
    console.log('👤 ADMIN-CREATED USER:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: testuser123');
    console.log('   Expected: Direct to dashboard (SKIP ONBOARDING)');
    console.log('');
    console.log('🌐 Access: http://localhost:3000');
    console.log('📊 RBAC Module: http://localhost:3000/dashboard/rbac');
    console.log('');
    console.log('🧪 TEST STEPS:');
    console.log('1. Login as Organization Admin');
    console.log('2. Go to RBAC module and create a new user');
    console.log('3. Logout and login as the newly created user');
    console.log('4. Verify user goes directly to dashboard (not onboarding)');

  } catch (error) {
    console.error('❌ Error in test setup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAdminUserCreation();
