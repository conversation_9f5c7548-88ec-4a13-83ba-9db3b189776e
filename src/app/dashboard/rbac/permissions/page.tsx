import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PermissionsManagementClient } from "@/components/rbac/permissions-management-client";

export const metadata: Metadata = {
  title: "Permissions | RBAC",
  description: "View and manage permissions for your organization",
};

export default async function PermissionsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin or platform admin
  if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return <PermissionsManagementClient />;
}
