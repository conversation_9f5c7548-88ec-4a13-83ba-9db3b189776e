import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { RolesManagementClient } from "@/components/rbac/roles-management-client";

export const metadata: Metadata = {
  title: "Roles | RBAC",
  description: "Create and manage custom roles for your organization",
};

export default async function RolesPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin or platform admin
  if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return <RolesManagementClient />;
}
