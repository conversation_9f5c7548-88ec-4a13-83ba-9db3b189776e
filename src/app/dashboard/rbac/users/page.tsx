import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { UsersManagementClient } from "@/components/rbac/users-management-client";

export const metadata: Metadata = {
  title: "Users | RBAC",
  description: "Manage organization users and their access",
};

export default async function UsersPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin or platform admin
  if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return <UsersManagementClient />;
}
