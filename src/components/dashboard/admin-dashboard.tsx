"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ard<PERSON>ontent,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList,
} from "@/components/ui/animated";
import { 
  Users, 
  Building2, 
  Activity, 
  Settings, 
  Shield, 
  BarChart3, 
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  Plus
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface AdminDashboardProps {
  userName: string;
}

interface AdminStats {
  totalUsers: number;
  totalOrganizations: number;
  totalProjects: number;
  pendingVerifications: number;
  systemHealth: "healthy" | "warning" | "critical";
  recentActivity: number;
}

export function AdminDashboard({ userName }: AdminDashboardProps) {
  const router = useRouter();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalOrganizations: 0,
    totalProjects: 0,
    pendingVerifications: 0,
    systemHealth: "healthy",
    recentActivity: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAdminStats = async () => {
      try {
        const response = await fetch("/api/admin/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error("Error fetching admin stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminStats();
  }, []);

  const getHealthIcon = () => {
    switch (stats.systemHealth) {
      case "healthy":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "critical":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const quickActions = [
    {
      title: "Manage Users",
      description: "View and manage all platform users",
      href: "/admin/users",
      icon: Users,
    },
    {
      title: "Organizations",
      description: "Manage organizations and verifications",
      href: "/admin/organizations",
      icon: Building2,
    },
    {
      title: "RBAC Management",
      description: "Manage roles and permissions",
      href: "/dashboard/rbac",
      icon: Shield,
    },
    {
      title: "System Settings",
      description: "Configure platform settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ];

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="page-header-spacing">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{`Welcome, ${userName}`}</h1>
            <p className="text-muted-foreground">Platform Administration Dashboard</p>
          </div>
        </div>

        {/* Admin Stats */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Total Users
              </AnimatedCardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalUsers.toLocaleString()}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Across all organizations
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Organizations
              </AnimatedCardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalOrganizations.toLocaleString()}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Active organizations
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Pending Verifications
              </AnimatedCardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.pendingVerifications}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Require attention
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                System Health
              </AnimatedCardTitle>
              {getHealthIcon()}
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold capitalize">{stats.systemHealth}</div>
              <p className="text-xs font-normal text-muted-foreground">
                All systems operational
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        {/* Quick Actions */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <StaggeredList className="col-span-4">
            <AnimatedCard className="col-span-4">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-[16px] font-medium leading-snug">Quick Actions</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm font-normal text-muted-foreground leading-relaxed">
                  Common administrative tasks
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {quickActions.map((action) => (
                    <Button
                      key={action.href}
                      variant="outline"
                      className="h-auto p-4 justify-start"
                      onClick={() => router.push(action.href)}
                    >
                      <div className="flex items-center gap-3">
                        <action.icon className="h-5 w-5" />
                        <div className="text-left">
                          <div className="font-medium">{action.title}</div>
                          <div className="text-xs text-muted-foreground">{action.description}</div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>

          <StaggeredList className="col-span-3">
            <AnimatedCard className="col-span-3">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-lg font-semibold leading-snug">Recent Activity</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm text-muted-foreground leading-relaxed">
                  Platform activity overview
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">System Activity</p>
                      <p className="text-xs text-muted-foreground">{stats.recentActivity} events in last 24h</p>
                    </div>
                  </div>
                  <AnimatedButton
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push("/admin/activity")}
                  >
                    View All Activity
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </AnimatedButton>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>
        </div>
      </div>
    </PageTransition>
  );
}
