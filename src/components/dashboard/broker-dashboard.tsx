"use client";

import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedCardDescription,
} from "@/components/ui/animated";
import { Handshake, Users, DollarSign, TrendingUp } from "lucide-react";

interface BrokerDashboardProps {
  userName: string;
}

export function BrokerDashboard({ userName }: BrokerDashboardProps) {
  const router = useRouter();

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="page-header-spacing">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{`Welcome, ${userName}`}</h1>
            <p className="text-muted-foreground">Broker Dashboard</p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Active Clients
              </AnimatedCardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">12</div>
              <p className="text-xs font-normal text-muted-foreground">
                +2 new this month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Total Commissions
              </AnimatedCardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">$45,231</div>
              <p className="text-xs font-normal text-muted-foreground">
                +20.1% from last month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Deals Closed
              </AnimatedCardTitle>
              <Handshake className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">23</div>
              <p className="text-xs font-normal text-muted-foreground">
                +5 this week
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Performance
              </AnimatedCardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">95%</div>
              <p className="text-xs font-normal text-muted-foreground">
                Client satisfaction rate
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        <AnimatedCard>
          <AnimatedCardHeader>
            <AnimatedCardTitle>Broker Features</AnimatedCardTitle>
            <AnimatedCardDescription>
              Access broker-specific functionality
            </AnimatedCardDescription>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <p className="text-muted-foreground">
              Broker dashboard features are being developed. Please check back soon for client management, 
              commission tracking, and deal management tools.
            </p>
          </AnimatedCardContent>
        </AnimatedCard>
      </div>
    </PageTransition>
  );
}
