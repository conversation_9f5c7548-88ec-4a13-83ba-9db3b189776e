"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  PageTransition,
  AnimatedCard,
  AnimatedCardHeader,
  Animated<PERSON>ardContent,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList,
} from "@/components/ui/animated";
import { 
  Users, 
  Leaf, 
  ShoppingCart, 
  Wallet, 
  Settings, 
  Shield, 
  Building2,
  FolderOpen,
  UserPlus,
  ArrowRight,
  Plus,
  Activity
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { CarbonCreditsWidget } from "@/components/dashboard/carbon-credits-widget";
import { ProfileCompletionBanner } from "@/components/dashboard/profile-completion-banner";

interface OrganizationAdminDashboardProps {
  userName: string;
}

interface OrgAdminStats {
  totalTeamMembers: number;
  totalProjects: number;
  totalCarbonCredits: number;
  activeListings: number;
  pendingInvitations: number;
  organizationHealth: "complete" | "incomplete";
}

export function OrganizationAdminDashboard({ userName }: OrganizationAdminDashboardProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [stats, setStats] = useState<OrgAdminStats>({
    totalTeamMembers: 0,
    totalProjects: 0,
    totalCarbonCredits: 0,
    activeListings: 0,
    pendingInvitations: 0,
    organizationHealth: "incomplete",
  });
  const [loading, setLoading] = useState(true);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.organizationId) {
      setOrganizationId(session.user.organizationId);
    }

    const fetchOrgAdminStats = async () => {
      try {
        // Fetch organization stats
        const response = await fetch("/api/dashboard/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error("Error fetching organization admin stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrgAdminStats();
  }, [session]);

  const quickActions = [
    {
      title: "Manage Team",
      description: "Add and manage team members",
      href: "/dashboard/rbac/users",
      icon: Users,
    },
    {
      title: "Create Project",
      description: "Start a new carbon project",
      href: "/dashboard/projects/create",
      icon: Plus,
    },
    {
      title: "Role Management",
      description: "Manage roles and permissions",
      href: "/dashboard/rbac",
      icon: Shield,
    },
    {
      title: "Organization Settings",
      description: "Configure organization settings",
      href: "/dashboard/settings",
      icon: Settings,
    },
  ];

  const managementActions = [
    {
      title: "Invite Team Member",
      description: "Add new users to your organization",
      action: () => router.push("/dashboard/rbac/users"),
      icon: UserPlus,
    },
    {
      title: "View Projects",
      description: "Manage all organization projects",
      action: () => router.push("/dashboard/projects"),
      icon: FolderOpen,
    },
    {
      title: "Browse Marketplace",
      description: "Explore carbon credit marketplace",
      action: () => router.push("/dashboard/marketplace"),
      icon: ShoppingCart,
    },
    {
      title: "Manage Wallets",
      description: "Organization wallet management",
      action: () => router.push("/dashboard/wallet"),
      icon: Wallet,
    },
  ];

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="page-header-spacing">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{`Welcome, ${userName}`}</h1>
            <p className="text-muted-foreground">Organization Administration Dashboard</p>
          </div>
        </div>

        {organizationId && stats.organizationHealth === "incomplete" && (
          <ProfileCompletionBanner organizationId={organizationId} />
        )}

        {/* Organization Stats */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Team Members
              </AnimatedCardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalTeamMembers}</div>
              <p className="text-xs font-normal text-muted-foreground">
                {stats.pendingInvitations > 0 ? `${stats.pendingInvitations} pending invitations` : "All members active"}
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Active Projects
              </AnimatedCardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalProjects}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Carbon credit projects
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Carbon Credits
              </AnimatedCardTitle>
              <Leaf className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalCarbonCredits.toLocaleString()}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Total credits generated
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Active Listings
              </AnimatedCardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.activeListings}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Marketplace listings
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        {/* Main Content */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <StaggeredList className="col-span-4">
            <AnimatedCard className="col-span-4">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-[16px] font-medium leading-snug">Carbon Credits Portfolio</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm font-normal text-muted-foreground leading-relaxed">
                  Your organization's carbon credit portfolio
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <CarbonCreditsWidget />
              </AnimatedCardContent>
              <AnimatedCardContent>
                <AnimatedButton
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/dashboard/carbon-credits")}
                >
                  View All Carbon Credits
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>

          <StaggeredList className="col-span-3">
            <AnimatedCard className="col-span-3">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-lg font-semibold leading-snug">Quick Actions</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm text-muted-foreground leading-relaxed">
                  Common administrative tasks
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-3">
                  {managementActions.map((action) => (
                    <Button
                      key={action.title}
                      className="w-full justify-start h-auto p-3"
                      variant="outline"
                      onClick={action.action}
                    >
                      <action.icon className="mr-3 h-4 w-4" />
                      <div className="text-left">
                        <div className="font-medium text-sm">{action.title}</div>
                        <div className="text-xs text-muted-foreground">{action.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>
        </div>

        {/* Management Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <AnimatedCard key={action.href} className="cursor-pointer hover:shadow-md transition-shadow">
              <AnimatedCardHeader className="card-padding">
                <div className="flex items-center gap-3">
                  <action.icon className="h-5 w-5 text-primary" />
                  <AnimatedCardTitle className="text-sm font-medium">{action.title}</AnimatedCardTitle>
                </div>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <p className="text-xs text-muted-foreground mb-3">{action.description}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => router.push(action.href)}
                >
                  Access
                  <ArrowRight className="ml-2 h-3 w-3" />
                </Button>
              </AnimatedCardContent>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </PageTransition>
  );
}
