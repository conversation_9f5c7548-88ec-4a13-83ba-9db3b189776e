"use client";

import { useSession } from "next-auth/react";
import { AdminDashboard } from "./admin-dashboard";
import { OrganizationAdminDashboard } from "./organization-admin-dashboard";
import { UserDashboard } from "./user-dashboard";
import { BrokerDashboard } from "./broker-dashboard";
import { SPVUserDashboard } from "./spv-user-dashboard";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface RoleSpecificDashboardProps {
  userName: string;
}

export function RoleSpecificDashboard({ userName }: RoleSpecificDashboardProps) {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">Please log in to view your dashboard.</p>
      </div>
    );
  }

  const userRole = session.user.role;

  switch (userRole) {
    case "ADMIN":
      return <AdminDashboard userName={userName} />;
    case "ORGANIZATION_ADMIN":
      return <OrganizationAdminDashboard userName={userName} />;
    case "BROKER":
      return <BrokerDashboard userName={userName} />;
    case "SPV_USER":
      return <SPVUserDashboard userName={userName} />;
    case "ORGANIZATION_USER":
    case "USER":
    default:
      return <UserDashboard userName={userName} />;
  }
}
