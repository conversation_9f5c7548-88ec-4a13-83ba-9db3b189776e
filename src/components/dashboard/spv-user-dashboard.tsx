"use client";

import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedCardDescription,
} from "@/components/ui/animated";
import { Building2, FolderOpen, Upload, CheckCircle } from "lucide-react";

interface SPVUserDashboardProps {
  userName: string;
}

export function SPVUserDashboard({ userName }: SPVUserDashboardProps) {
  const router = useRouter();

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="page-header-spacing">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{`Welcome, ${userName}`}</h1>
            <p className="text-muted-foreground">SPV User Dashboard</p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Assigned Projects
              </AnimatedCardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">5</div>
              <p className="text-xs font-normal text-muted-foreground">
                Active projects
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Data Entries
              </AnimatedCardTitle>
              <Upload className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">142</div>
              <p className="text-xs font-normal text-muted-foreground">
                This month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Verified Entries
              </AnimatedCardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">138</div>
              <p className="text-xs font-normal text-muted-foreground">
                97% verification rate
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                SPV Status
              </AnimatedCardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">Active</div>
              <p className="text-xs font-normal text-muted-foreground">
                All systems operational
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        <AnimatedCard>
          <AnimatedCardHeader>
            <AnimatedCardTitle>SPV Features</AnimatedCardTitle>
            <AnimatedCardDescription>
              Access SPV-specific functionality
            </AnimatedCardDescription>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <p className="text-muted-foreground">
              SPV dashboard features are being developed. Please check back soon for project management, 
              data entry tools, and verification workflows.
            </p>
          </AnimatedCardContent>
        </AnimatedCard>
      </div>
    </PageTransition>
  );
}
