"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  PageTransition,
  Animated<PERSON>ard,
  Animated<PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnimatedCard<PERSON>escription,
  AnimatedButton,
  StaggeredList,
} from "@/components/ui/animated";
import { 
  Leaf, 
  ShoppingCart, 
  Wallet, 
  Activity,
  ArrowRight,
  Plus,
  Eye
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { CarbonCreditsWidget } from "@/components/dashboard/carbon-credits-widget";
import { TransactionHistory } from "@/components/dashboard/transaction-history";
import { ProfileCompletionIndicator } from "@/components/profile/profile-completion-indicator";
import { formatCurrency, truncateAddress } from "@/lib/utils";

interface UserDashboardProps {
  userName: string;
}

interface UserStats {
  totalCarbonCredits: number;
  activeListings: number;
  walletBalance: number;
  recentTransactions: number;
}

interface WalletData {
  id: string;
  address: string;
  balance: number;
  network: string;
  isTestnet: boolean;
}

export function UserDashboard({ userName }: UserDashboardProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [stats, setStats] = useState<UserStats>({
    totalCarbonCredits: 0,
    activeListings: 0,
    walletBalance: 0,
    recentTransactions: 0,
  });
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [loading, setLoading] = useState(true);
  const [walletLoading, setWalletLoading] = useState(true);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.organizationId) {
      setOrganizationId(session.user.organizationId);
    }

    const fetchUserStats = async () => {
      try {
        const response = await fetch("/api/dashboard/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error("Error fetching user stats:", error);
      } finally {
        setLoading(false);
      }
    };

    const fetchWallet = async () => {
      try {
        const response = await fetch("/api/wallet");
        if (response.ok) {
          const data = await response.json();
          setWallet(data.wallet);
        }
      } catch (error) {
        console.error("Error fetching wallet:", error);
      } finally {
        setWalletLoading(false);
      }
    };

    fetchUserStats();
    fetchWallet();
  }, [session]);

  const quickActions = [
    {
      title: "Create Carbon Credit",
      description: "Generate new carbon credits",
      action: () => router.push("/dashboard/carbon-credits/create"),
      icon: Plus,
    },
    {
      title: "Browse Marketplace",
      description: "Explore available carbon credits",
      action: () => router.push("/dashboard/marketplace"),
      icon: ShoppingCart,
    },
    {
      title: "Manage Wallet",
      description: "View and manage your wallet",
      action: () => router.push("/dashboard/wallet"),
      icon: Wallet,
    },
    {
      title: "View Transactions",
      description: "Check your transaction history",
      action: () => router.push("/dashboard/transactions"),
      icon: Activity,
    },
  ];

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="page-header-spacing">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{`Welcome, ${userName}`}</h1>
            <p className="text-muted-foreground">Your carbon trading dashboard</p>
          </div>
        </div>

        {/* User Stats */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Carbon Credits
              </AnimatedCardTitle>
              <Leaf className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.totalCarbonCredits.toLocaleString()}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Total credits owned
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Active Listings
              </AnimatedCardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.activeListings}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Credits for sale
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Wallet Balance
              </AnimatedCardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">
                {walletLoading ? "Loading..." : wallet ? formatCurrency(wallet.balance, wallet.network) : "No Wallet"}
              </div>
              <p className="text-xs font-normal text-muted-foreground">
                {walletLoading ? "Fetching balance..." : wallet ? `${truncateAddress(wallet.address)} • ${wallet.isTestnet ? "Testnet" : "Mainnet"}` : "Create wallet to get started"}
              </p>
            </AnimatedCardContent>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0">
              <AnimatedCardTitle className="text-sm font-normal">
                Recent Activity
              </AnimatedCardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-xl font-bold">{loading ? "..." : stats.recentTransactions}</div>
              <p className="text-xs font-normal text-muted-foreground">
                Transactions this month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        {/* Main Content */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <StaggeredList className="col-span-4">
            <AnimatedCard className="col-span-4">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-[16px] font-medium leading-snug">Carbon Credits</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm font-normal text-muted-foreground leading-relaxed">
                  Your carbon credit portfolio
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <CarbonCreditsWidget />
              </AnimatedCardContent>
              <AnimatedCardFooter>
                <AnimatedButton
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/dashboard/carbon-credits")}
                >
                  View All Carbon Credits
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </AnimatedCardFooter>
            </AnimatedCard>
          </StaggeredList>

          <StaggeredList className="col-span-3">
            <AnimatedCard className="col-span-3">
              <AnimatedCardHeader className="card-padding">
                <AnimatedCardTitle className="text-lg font-semibold leading-snug">Quick Actions</AnimatedCardTitle>
                <AnimatedCardDescription className="text-sm text-muted-foreground leading-relaxed">
                  Common tasks and actions
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-3">
                  {quickActions.map((action) => (
                    <Button
                      key={action.title}
                      className="w-full justify-start h-auto p-3"
                      variant="outline"
                      onClick={action.action}
                    >
                      <action.icon className="mr-3 h-4 w-4" />
                      <div className="text-left">
                        <div className="font-medium text-sm">{action.title}</div>
                        <div className="text-xs text-muted-foreground">{action.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>
        </div>

        {/* Additional Content */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <TransactionHistory className="col-span-2" />

          <AnimatedCard>
            <AnimatedCardHeader className="card-padding">
              <AnimatedCardTitle className="text-lg font-semibold leading-snug">Profile Status</AnimatedCardTitle>
              <AnimatedCardDescription className="text-sm text-muted-foreground leading-relaxed">
                Complete your profile to unlock all features
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {organizationId ? (
                <ProfileCompletionIndicator
                  organizationId={organizationId}
                  showDetails={true}
                />
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Organization information not available
                  </p>
                </div>
              )}
            </AnimatedCardContent>
            <AnimatedCardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/settings/profile")}
              >
                Update Profile
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </AnimatedCardFooter>
          </AnimatedCard>
        </div>
      </div>
    </PageTransition>
  );
}
